#!/usr/bin/env python3
"""
Test the enhanced external search functionality
"""

import sys
import os
sys.path.append('.')

from stream import StreamlitSQLQuery

def test_landlord_friendly_query():
    """Test the landlord-friendly states query"""
    print("🚀 Testing Enhanced External Search Functionality\n")
    
    # Initialize the query tool (without Streamlit context)
    try:
        # Mock the streamlit session state and other dependencies
        import streamlit as st
        if 'user_role' not in st.session_state:
            st.session_state.user_role = 'user'
        
        query_tool = StreamlitSQLQuery()
    except Exception as e:
        print(f"❌ Could not initialize StreamlitSQLQuery: {e}")
        return
    
    # Test question that requires external search
    test_question = "Give me all the states that are landlord-friendly and show me the highest ZH Ratio"
    
    print(f"🔍 Testing question: '{test_question}'\n")
    
    # Test validation
    print("1️⃣ Testing validation...")
    validation = query_tool.validate_and_suggest(test_question)
    print(f"   Valid: {validation['valid']}")
    print(f"   Needs External Search: {validation.get('needs_external_search', False)}")
    
    if validation.get('needs_external_search', False):
        print("\n2️⃣ Testing external data search...")
        external_info = query_tool.handle_external_data_query(test_question)
        print("   External search completed!")
        print(f"   Response length: {len(external_info)} characters")
        print(f"   First 200 characters: {external_info[:200]}...")
    
    print("\n3️⃣ Testing enhanced SQL generation...")
    sql_query = query_tool.enhanced_natural_language_to_sql(test_question)
    print(f"   Generated SQL: {sql_query}")
    
    print("\n✅ Test completed!")

def test_empty_results_search():
    """Test what happens when a query returns no results"""
    print("\n" + "="*60)
    print("🔍 Testing Empty Results External Search\n")
    
    try:
        query_tool = StreamlitSQLQuery()
    except Exception as e:
        print(f"❌ Could not initialize StreamlitSQLQuery: {e}")
        return
    
    # Test question that will likely return no results
    test_question = "Show me cities in landlord-friendly states with high crime rates"
    
    print(f"🔍 Testing question: '{test_question}'\n")
    
    # Test the search for missing data function
    print("Testing search_for_missing_data...")
    explanation = "No results found because the database doesn't contain crime rate data or landlord-friendly state classifications."
    
    external_search_result = query_tool.search_for_missing_data(test_question, explanation)
    print("   External search for missing data completed!")
    print(f"   Response length: {len(external_search_result)} characters")
    print(f"   First 300 characters: {external_search_result[:300]}...")
    
    print("\n✅ Empty results search test completed!")

if __name__ == "__main__":
    # Set up minimal environment
    os.environ.setdefault('STREAMLIT_SERVER_HEADLESS', 'true')
    
    try:
        test_landlord_friendly_query()
        test_empty_results_search()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
