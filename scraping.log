2025-07-25 15:30:58,548 - INFO - ✅ HUD API configured with base URL: https://www.huduser.gov/hudapi/public
2025-07-25 15:30:58,548 - INFO - 🔧 Validating configuration...
2025-07-25 15:30:58,548 - INFO - ✅ Configuration validation passed
2025-07-25 15:30:58,548 - INFO -    - HUD API: https://www.huduser.gov/hudapi/public
2025-07-25 15:30:58,549 - INFO -    - Gemini API: Configured
2025-07-25 15:30:58,549 - INFO - 🚀 Initializing Partners 8 Data Pipeline
2025-07-25 15:30:58,549 - INFO - 🚀 Starting Partners 8 Complete Data Pipeline
2025-07-25 15:30:58,549 - INFO - 📥 STEP 1: Downloading Zillow Data
2025-07-25 15:30:58,550 - INFO - 🏠 Downloading Zillow data...
2025-07-25 15:30:58,550 - INFO - Downloading Zillow Home Value Index - City Level...
2025-07-25 15:30:58,552 - ERROR - ❌ Failed to download ZHVI data: No connection adapters were found for '[https://files.zillowstatic.com/research/public_csvs/zhvi/City_zhvi_uc_sfrcondo_tier_0.33_0.67_sm_sa_month.csv](https://files.zillowstatic.com/research/public_csvs/zhvi/City_zhvi_uc_sfrcondo_tier_0.33_0.67_sm_sa_month.csv)'
2025-07-25 15:30:58,552 - ERROR - Pipeline failed: Failed to download Zillow data
2025-07-25 15:30:58,553 - ERROR - Traceback (most recent call last):
  File "/home/<USER>/projects/tool/main.py", line 955, in main
    pipeline_success = pipeline.run_complete_pipeline()
  File "/home/<USER>/projects/tool/main.py", line 877, in run_complete_pipeline
    if not self.step1_download_zillow_data(): return False
  File "/home/<USER>/projects/tool/main.py", line 686, in step1_download_zillow_data
    raise Exception("Failed to download Zillow data")
Exception: Failed to download Zillow data

