#!/usr/bin/env python3
"""
Test script for external search functionality
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_search():
    """Test the Gemini API with Google Search grounding"""
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"
    
    headers = {'Content-Type': 'application/json'}
    
    # Test prompt for landlord-friendly states
    prompt = """
You are a real estate data analyst. The user asked: "Give me all the states that are landlord-friendly and show me the highest ZH Ratio"

This question requires external information not in our database. Use Google Search to find:
1. Current information about landlord-friendly states, rent control laws, or other external real estate factors
2. Any other relevant external data needed to answer the question
3. Lists of states, cities, or regions that match the criteria mentioned in the question

After getting the external information, provide:
1. The external data you found (e.g., specific list of landlord-friendly states with their abbreviations)
2. A suggested SQL query to filter our database using this information
3. An explanation of how to combine the external data with our database

Our database contains: rent prices, home values, income limits, city/state info, rent-to-value ratios.

IMPORTANT: When providing state lists, use 2-letter state abbreviations (CA, TX, NY, FL, etc.) that can be used directly in SQL IN clauses.
"""
    
    data = {
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 4096,
            "candidateCount": 1
        },
        "tools": [{"googleSearch": {}}]  # Enable Google Search
    }
    
    try:
        print("🔍 Testing Gemini API with Google Search...")
        response = requests.post(
            f"{url}?key={api_key}",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                print("✅ Google Search integration working!")

                # Save full response to file
                with open('test_response.txt', 'w') as f:
                    f.write(content)

                print(f"\n📋 Response saved to test_response.txt (Length: {len(content)} characters)")
                print("\n📋 First 500 characters:")
                print(content[:500])
                
                # Check if grounding metadata is present
                if 'groundingMetadata' in result['candidates'][0]:
                    metadata = result['candidates'][0]['groundingMetadata']
                    print(f"\n🌐 Search queries used: {metadata.get('webSearchQueries', [])}")
                    print(f"📚 Sources found: {len(metadata.get('groundingChunks', []))}")
                
                return True
            else:
                print("❌ No response generated")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling Gemini API: {e}")
        return False

def test_basic_gemini():
    """Test basic Gemini API without search"""
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"
    
    headers = {'Content-Type': 'application/json'}
    data = {
        "contents": [{"parts": [{"text": "Hello, can you respond with 'API is working'?"}]}],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 100,
            "candidateCount": 1
        }
    }
    
    try:
        print("🔍 Testing basic Gemini API...")
        response = requests.post(
            f"{url}?key={api_key}",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ Basic API working! Response: {content}")
                return True
            else:
                print("❌ No response generated")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling Gemini API: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing External Search Functionality\n")
    
    # Test basic API first
    if test_basic_gemini():
        print("\n" + "="*50 + "\n")
        # Test search functionality
        test_gemini_search()
    else:
        print("❌ Basic API test failed, skipping search test")
    
    print("\n✅ Test completed!")
